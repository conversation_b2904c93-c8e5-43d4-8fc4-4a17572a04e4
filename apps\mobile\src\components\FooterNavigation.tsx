import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface FooterNavigationProps {
  navigation: any;
  activeScreen: string;
}

export default function FooterNavigation({ navigation, activeScreen }: FooterNavigationProps) {
  const tabs = [
    { name: 'Home', icon: 'home', screen: 'Home' },
    { name: 'Orders', icon: 'receipt-outline', screen: 'Orders' },
    { name: 'Cart', icon: 'bag-outline', screen: 'Cart' },
    { name: 'Search', icon: 'search-outline', screen: 'Search' },
    { name: 'Account', icon: 'person-outline', screen: 'Account' },
  ];

  return (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#FFFFFF',
      borderTopWidth: 1,
      borderTopColor: '#E5E5E5',
      paddingTop: 12,
      paddingBottom: 12,
      paddingHorizontal: 8,
      minHeight: 70,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 8, // Android shadow
    }}>
      {tabs.map((tab, index) => (
        <TouchableOpacity
          key={tab.name}
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 4,
          }}
          onPress={() => {
            if (tab.screen !== activeScreen) {
              navigation.navigate(tab.screen);
            }
          }}
        >
          {tab.name === 'Cart' ? (
            // Special elevated circular design for Cart
            <View style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: activeScreen === tab.screen ? '#f97316' : '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: -8, // Elevate above other tabs
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 3,
              },
              shadowOpacity: 0.15,
              shadowRadius: 6,
              elevation: 6, // Android shadow
              borderWidth: activeScreen === tab.screen ? 0 : 1,
              borderColor: '#E5E5E5',
            }}>
              <Ionicons
                name="cart-outline"
                size={22}
                color={activeScreen === tab.screen ? '#FFFFFF' : '#666'}
              />
            </View>
          ) : (
            // Regular design for other tabs
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={activeScreen === tab.screen ? '#f97316' : '#666'}
            />
          )}
          <Text style={{
            fontSize: 10,
            marginTop: tab.name === 'Cart' ? 6 : 2, // Extra spacing for elevated cart
            fontWeight: '500',
            color: activeScreen === tab.screen ? '#f97316' : '#666',
          }}>
            {tab.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}
