import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { CartProvider } from './src/contexts/CartContext';
import './global.css';

export default function App() {
  return (
    <SafeAreaProvider>
      <CartProvider>
        {/* Full screen container with brand color extending to status bar */}
        <View style={{ flex: 1, backgroundColor: '#f3a823' }}>
          <NavigationContainer>
            <AppNavigator />
            {/* Status bar with light content for dark background */}
            <StatusBar style="light" backgroundColor="#f3a823" translucent={false} />
          </NavigationContainer>
        </View>
      </CartProvider>
    </SafeAreaProvider>
  );
}
