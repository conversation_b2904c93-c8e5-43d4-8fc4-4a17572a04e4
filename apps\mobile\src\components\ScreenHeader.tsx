import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useResponsiveStyles, createResponsiveSpacing, createResponsiveFontSize } from '../hooks/useResponsiveStyles';

interface ScreenHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
}

export default function ScreenHeader({
  title,
  showBackButton = false,
  onBackPress,
  rightComponent,
  backgroundColor = '#f3a823'
}: ScreenHeaderProps) {
  const styles = useResponsiveStyles((screenInfo) => ({
    container: {
      backgroundColor: backgroundColor,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: createResponsiveSpacing(screenInfo, 20),
      paddingTop: createResponsiveSpacing(screenInfo, 10),
      paddingBottom: createResponsiveSpacing(screenInfo, 20),
      minHeight: createResponsiveSpacing(screenInfo, 60),
    },
    leftSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    backButton: {
      marginRight: createResponsiveSpacing(screenInfo, 12),
      padding: createResponsiveSpacing(screenInfo, 4),
    },
    title: {
      fontSize: createResponsiveFontSize(screenInfo, 28),
      fontWeight: 'bold',
      color: 'white',
      flex: 1,
    },
    rightSection: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  }));

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
          )}
          <Text style={styles.title}>{title}</Text>
        </View>
        {rightComponent && (
          <View style={styles.rightSection}>
            {rightComponent}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}
